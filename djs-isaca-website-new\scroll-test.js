// Scroll Test Script - Run this in browser console to test section visibility
console.log('🔍 Starting scroll visibility test...');

// Function to check if sections are visible
function checkSectionVisibility() {
  const sections = ['#about', '#features', '.team-section', '.testimonials-section', '.contact-section'];
  const results = {};
  
  sections.forEach(selector => {
    const element = document.querySelector(selector);
    if (element) {
      const rect = element.getBoundingClientRect();
      const styles = window.getComputedStyle(element);
      
      results[selector] = {
        exists: true,
        visible: styles.visibility === 'visible',
        opacity: styles.opacity,
        display: styles.display,
        transform: styles.transform,
        inViewport: rect.top < window.innerHeight && rect.bottom > 0,
        rect: {
          top: rect.top,
          bottom: rect.bottom,
          height: rect.height
        }
      };
    } else {
      results[selector] = { exists: false };
    }
  });
  
  return results;
}

// Initial check
console.log('📊 Initial section visibility:', checkSectionVisibility());

// Monitor during scroll
let scrollCount = 0;
window.addEventListener('scroll', () => {
  scrollCount++;
  
  // Check every 10 scroll events to avoid spam
  if (scrollCount % 10 === 0) {
    const results = checkSectionVisibility();
    console.log(`📊 Scroll check #${scrollCount/10}:`, results);
    
    // Alert if any section becomes hidden
    Object.entries(results).forEach(([selector, data]) => {
      if (data.exists && (data.opacity !== '1' || data.visible === false || data.display === 'none')) {
        console.error(`🚨 SECTION HIDDEN: ${selector}`, data);
        alert(`⚠️ Section ${selector} became hidden! Check console for details.`);
      }
    });
  }
});

// Auto-scroll test
function autoScrollTest() {
  console.log('🤖 Starting auto-scroll test...');
  
  let currentScroll = 0;
  const maxScroll = document.body.scrollHeight - window.innerHeight;
  const scrollStep = 100;
  
  const scrollInterval = setInterval(() => {
    currentScroll += scrollStep;
    window.scrollTo(0, currentScroll);
    
    if (currentScroll >= maxScroll) {
      clearInterval(scrollInterval);
      console.log('✅ Auto-scroll test completed!');
      
      // Final check
      setTimeout(() => {
        const finalResults = checkSectionVisibility();
        console.log('📊 Final visibility check:', finalResults);
        
        const hiddenSections = Object.entries(finalResults).filter(([_, data]) => 
          data.exists && (data.opacity !== '1' || data.visible === false || data.display === 'none')
        );
        
        if (hiddenSections.length === 0) {
          console.log('🎉 SUCCESS: All sections remained visible during scroll!');
          alert('🎉 SUCCESS: Your arm is safe! All sections remained visible during scroll!');
        } else {
          console.error('❌ FAILURE: Some sections became hidden:', hiddenSections);
          alert('❌ FAILURE: Some sections became hidden. Check console for details.');
        }
      }, 1000);
    }
  }, 100);
}

// Export functions for manual testing
window.scrollTest = {
  check: checkSectionVisibility,
  autoTest: autoScrollTest
};

console.log('✅ Scroll test script loaded!');
console.log('💡 Usage:');
console.log('  - scrollTest.check() - Check current visibility');
console.log('  - scrollTest.autoTest() - Run automatic scroll test');
