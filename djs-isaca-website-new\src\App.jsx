import { useState, useRef } from 'react';
import LoadingScreen from './components/LoadingScreen';
import HeroSection from './components/HeroSection';
import Navigation from './components/Navigation';
import TestimonialsSection from './components/TestimonialsSection';
import TeamSection from './components/TeamSection';
import ContactSection from './components/ContactSection';
import './styles/main.css';

function App() {
  const [loading, setLoading] = useState(true);
  const aboutRef = useRef();
  const featuresRef = useRef();
  const footerRef = useRef();

  const handleLoadingComplete = () => {
    setLoading(false);
  };

  return (
    <div className="app-bg">
      {loading ? (
        <LoadingScreen onLoadingComplete={handleLoadingComplete} />
      ) : (
        <div>
          {/* Simple floating particles */}
          <div className="floating-particles">
            <div className="particle"></div>
            <div className="particle"></div>
            <div className="particle"></div>
            <div className="particle"></div>
            <div className="particle"></div>
          </div>

          <Navigation />
          <HeroSection />

          {/* Simple Cyber Decoration */}
          <div className="cyber-decoration">
            <div className="cyber-line"></div>
            <div className="cyber-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div className="cyber-line"></div>
          </div>

          <section id="about" className="about" ref={aboutRef}>
            <h2>About DJS ISACA</h2>
            <p>
              Founded by passionate cybersecurity enthusiasts at DJSCE, we're more than just a student chapter—we're a community of learners and security enthusiasts. Our mission is to bridge the gap between academic learning and real-world cybersecurity challenges, helping students develop practical skills in this critical field through hands-on experience, industry insights, and cutting-edge training.
            </p>
          </section>

          <section id="features" className="features" ref={featuresRef}>
            <h2>Our Impact Areas</h2>
            <div className="feature-grid">
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-shield-alt"></i></div>
                <h3>Hands-On Security Training</h3>
                <p>Interactive workshops covering penetration testing, vulnerability assessment, digital forensics, and the latest security tools used by industry professionals.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-flag"></i></div>
                <h3>Competitive CTF Events</h3>
                <p>Regular Capture The Flag competitions that challenge students with real-world security scenarios, from web exploitation to cryptography puzzles.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-users"></i></div>
                <h3>Industry Networking</h3>
                <p>Direct connections with cybersecurity professionals, guest lectures from industry experts, and mentorship opportunities to learn from experienced practitioners.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-certificate"></i></div>
                <h3>Professional Certifications</h3>
                <p>Guidance and preparation for industry-standard certifications like CEH, CISSP, and CompTIA Security+, helping students validate their cybersecurity knowledge.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-code"></i></div>
                <h3>Secure Development</h3>
                <p>Training in secure coding practices, application security testing, and DevSecOps methodologies essential for modern software development.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-brain"></i></div>
                <h3>Research & Innovation</h3>
                <p>Collaborative research projects in emerging security technologies like AI security, IoT protection, and blockchain security solutions.</p>
              </div>
            </div>
          </section>

          {/* Cyber Divider */}
          <div className="cyber-divider">
            <div className="cyber-grid-pattern"></div>
          </div>

          <TestimonialsSection />
          <TeamSection />
          <ContactSection />

          <footer className="footer" ref={footerRef}>
            <p>&copy; 2024 DJS ISACA Student Chapter | Building Tomorrow's Cybersecurity Leaders</p>
          </footer>
        </div>
      )}
    </div>
  );
}

export default App;
