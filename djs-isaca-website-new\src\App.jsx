import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import LoadingScreen from './components/LoadingScreen';
import HeroSection from './components/HeroSection';
import Animated3D from './components/Animated3D';
import ThreeScene from './components/ThreeScene';
import Navigation from './components/Navigation';
// ScrollAnimations removed to fix loading issues
import TestimonialsSection from './components/TestimonialsSection';
import TeamSection from './components/TeamSection';
import ContactSection from './components/ContactSection';
import './styles/main.css';

function ErrorBoundary({ children }) {
  const [error, setError] = React.useState(null);
  React.useEffect(() => {
    window.onerror = (msg, url, line, col, err) => setError(err || msg);
    return () => { window.onerror = null; };
  }, []);
  if (error) {
    return <div style={{ color: 'red', padding: '2rem' }}>Something went wrong: {error.message || error}</div>;
  }
  return children;
}

function App() {
  const [loading, setLoading] = useState(true);
  const [contentReady, setContentReady] = useState(false);
  const appRef = useRef();
  const headerRef = useRef();
  const aboutRef = useRef();
  const featuresRef = useRef();
  const footerRef = useRef();

  const handleLoadingComplete = () => {
    setLoading(false);
    // Small delay to ensure DOM is ready
    setTimeout(() => {
      setContentReady(true);
    }, 100);
  };

  useEffect(() => {
    if (contentReady && !loading) {
      // Ensure all content is immediately visible without animations
      gsap.set(appRef.current, { opacity: 1 });

      // CRITICAL FIX: Force all sections to be visible immediately and prevent disappearing
      const forceVisibility = (element, elementName) => {
        if (element) {
          // GSAP settings
          gsap.set(element, {
            opacity: 1,
            y: 0,
            visibility: 'visible',
            display: 'block',
            transform: 'none'
          });

          // Direct CSS fallback - CRITICAL for preventing disappearing content
          element.style.opacity = '1';
          element.style.visibility = 'visible';
          element.style.display = 'block';
          element.style.transform = 'translateY(0)';
          element.style.transition = 'none';
          element.style.animation = 'none';
          element.style.willChange = 'auto';

          console.log(`✅ ${elementName} section forced visible`);
        }
      };

      // Apply visibility fixes to all sections
      forceVisibility(aboutRef.current, 'About');
      forceVisibility(featuresRef.current, 'Features');
      forceVisibility(footerRef.current, 'Footer');

      // Special handling for feature cards
      if (featuresRef.current) {
        const featureCards = featuresRef.current.querySelectorAll('.feature-card');
        if (featureCards.length > 0) {
          gsap.set(featureCards, { opacity: 1, y: 0, scale: 1 });
          featureCards.forEach((card) => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0) scale(1)';
            card.style.visibility = 'visible';
            card.style.display = 'block';
          });
          console.log(`✅ ${featureCards.length} feature cards forced visible`);
        }
      }

      // Add scroll event listener to monitor and fix any disappearing content
      const handleScroll = () => {
        // Re-force visibility if any section becomes hidden
        [aboutRef.current, featuresRef.current, footerRef.current].forEach((ref, index) => {
          if (ref && (ref.style.opacity !== '1' || ref.style.visibility === 'hidden')) {
            console.warn(`⚠️ Section ${index} became hidden during scroll - fixing...`);
            forceVisibility(ref, `Section-${index}`);
          }
        });
      };

      window.addEventListener('scroll', handleScroll, { passive: true });

      // Cleanup
      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, [contentReady, loading]);

  return (
    <div className="app-bg">
      {loading ? (
        <LoadingScreen onLoadingComplete={handleLoadingComplete} />
      ) : (
        <div ref={appRef} style={{ opacity: 1 }}>
          <Navigation />
          {/* ScrollAnimations removed for immediate loading */}
          <HeroSection />
          <Animated3D />
          <ThreeScene />

          <section id="about" className="about" ref={aboutRef}>
            <h2>About DJS ISACA</h2>
            <p>
              Founded by passionate cybersecurity enthusiasts at DJSCE, we're more than just a student chapter—we're a community of learners and security enthusiasts. Our mission is to bridge the gap between academic learning and real-world cybersecurity challenges, helping students develop practical skills in this critical field through hands-on experience, industry insights, and cutting-edge training.
            </p>
          </section>

          <section id="features" className="features" ref={featuresRef}>
            <h2>Our Impact Areas</h2>
            <div className="feature-grid">
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-shield-alt"></i></div>
                <h3>Hands-On Security Training</h3>
                <p>Interactive workshops covering penetration testing, vulnerability assessment, digital forensics, and the latest security tools used by industry professionals.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-flag"></i></div>
                <h3>Competitive CTF Events</h3>
                <p>Regular Capture The Flag competitions that challenge students with real-world security scenarios, from web exploitation to cryptography puzzles.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-users"></i></div>
                <h3>Industry Networking</h3>
                <p>Direct connections with cybersecurity professionals, guest lectures from industry experts, and mentorship opportunities to learn from experienced practitioners.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-certificate"></i></div>
                <h3>Professional Certifications</h3>
                <p>Guidance and preparation for industry-standard certifications like CEH, CISSP, and CompTIA Security+, helping students validate their cybersecurity knowledge.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-code"></i></div>
                <h3>Secure Development</h3>
                <p>Training in secure coding practices, application security testing, and DevSecOps methodologies essential for modern software development.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon"><i className="fas fa-brain"></i></div>
                <h3>Research & Innovation</h3>
                <p>Collaborative research projects in emerging security technologies like AI security, IoT protection, and blockchain security solutions.</p>
              </div>
            </div>
          </section>

          <TestimonialsSection />
          <TeamSection />
          <ContactSection />

          <footer className="footer" ref={footerRef}>
            <p>&copy; 2024 DJS ISACA Student Chapter | Building Tomorrow's Cybersecurity Leaders</p>
          </footer>
        </div>
      )}
    </div>
  );
}

export default App;
