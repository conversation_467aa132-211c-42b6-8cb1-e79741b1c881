<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DJS ISACA - Cybersecurity Student Chapter</title>
    <meta name="description" content="DJS ISACA is DJSCE's premier cybersecurity student chapter, fostering knowledge and awareness in information security through workshops, CTF competitions, and community building.">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Cybersecurity Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>

    <!-- 🚨 EMERGENCY NUCLEAR VISIBILITY FIX 🚨 -->
    <script>
        console.log('🚨 EMERGENCY SCRIPT LOADED - PROTECTING YOUR LIMBS!');

        function emergencyVisibilityFix() {
            // NUCLEAR OPTION: Force everything to be visible
            const allSections = document.querySelectorAll('section, div, main, article');
            allSections.forEach(el => {
                if (el.id !== 'root' && !el.classList.contains('cyber-loading-screen')) {
                    el.style.opacity = '1';
                    el.style.visibility = 'visible';
                    el.style.display = 'block';
                    el.style.transform = 'none';
                    el.style.position = 'relative';
                    el.style.zIndex = '1';
                }
            });

            // Force body to be visible
            document.body.style.opacity = '1';
            document.body.style.visibility = 'visible';
            document.body.style.background = '#0B1426';
        }

        // Apply immediately when DOM loads
        document.addEventListener('DOMContentLoaded', emergencyVisibilityFix);

        // Apply on scroll
        window.addEventListener('scroll', emergencyVisibilityFix);

        // Apply every 200ms as backup
        setInterval(emergencyVisibilityFix, 200);

        console.log('🛡️ Emergency protection active!');
    </script>
</body>
</html>
