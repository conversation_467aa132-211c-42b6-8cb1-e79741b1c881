/* CSS Custom Properties for Professional Cybersecurity Theme */
:root {
  /* Professional Cybersecurity Color Palette */
  --primary-blue: #0B1426;
  --secondary-blue: #1E3A5F;
  --accent-blue: #2E5984;
  --cyber-green: #00D4AA;
  --security-orange: #FF6B35;
  --warning-red: #E74C3C;
  --neutral-gray: #2C3E50;
  --light-gray: #BDC3C7;
  --dark-gray: #34495E;
  --white: #FFFFFF;
  --off-white: #F8F9FA;

  /* Typography Scale */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-cyber: 0 0 20px rgba(0, 212, 170, 0.3);
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px; /* Account for fixed nav */
}

body, html, #root {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: 1.6;
  /* EMERGENCY FIX: Simplified background to prevent blank screen */
  background: var(--primary-blue);
  color: var(--off-white);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* CRITICAL: Prevent any rendering issues */
  contain: none !important;
  transform: none !important;
  backface-visibility: visible !important;
}

/* Ensure sections have proper spacing and don't overlap */
section {
  position: relative;
  z-index: 1;
  min-height: fit-content;
  /* Force visibility during scrolling - CRITICAL FOR PREVENTING DISAPPEARING CONTENT */
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  /* Prevent any transform or animation interference */
  transform: none !important;
  /* Ensure proper layout flow */
  contain: none !important;
  will-change: auto !important;
}

/* NUCLEAR OPTION: Ensure sections are ALWAYS visible */
.about, .features, .team, .testimonials, .contact,
.team-section, .testimonials-section, .contact-section,
section, div, main, article {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  /* NUCLEAR SAFEGUARDS against disappearing content */
  transform: none !important;
  transition: none !important;
  animation: none !important;
  position: relative !important;
  z-index: 1 !important;
  contain: none !important;
  will-change: auto !important;
  backface-visibility: visible !important;
  /* PREVENT ANY POSSIBLE HIDING */
  overflow: visible !important;
  clip: none !important;
  clip-path: none !important;
}

.content-ready .about,
.content-ready .features {
  opacity: 1;
  transform: translateY(0);
}

/* EMERGENCY FIX: Completely rewrite app background to prevent blank screen */
.app-bg {
  position: relative;
  min-height: 100vh;
  width: 100%;
  /* CRITICAL: Simple background that won't cause issues */
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  /* PREVENT SCROLL ISSUES */
  overflow-x: hidden;
  overflow-y: visible;
  /* FORCE VISIBILITY */
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* NUCLEAR OPTION: Force all content to be visible */
.app-bg * {
  /* Disable any problematic CSS properties */
  backface-visibility: visible !important;
  contain: none !important;
  will-change: auto !important;
}

/* EMERGENCY: Ensure main content container is always visible */
.app-bg > div:not(.cyber-loading-screen) {
  /* FORCE ABSOLUTE VISIBILITY */
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  position: relative !important;
  width: 100% !important;
  min-height: 100vh !important;
  /* PREVENT ANY TRANSFORMS THAT COULD HIDE CONTENT */
  transform: none !important;
  transition: none !important;
  animation: none !important;
}

/* EMERGENCY: Remove problematic background overlay that might cause blank screen */
.app-bg::before {
  /* DISABLED - this was causing the blank screen issue */
  display: none !important;
}

/* Professional Header Styling */
.header {
  text-align: center;
  margin-top: var(--space-8);
  padding: 0 var(--space-4);
  max-width: 100%;
}

.header h1 {
  font-size: clamp(var(--text-4xl), 6vw, var(--text-6xl));
  font-weight: var(--font-extrabold);
  letter-spacing: -0.02em;
  color: var(--white);
  margin-bottom: var(--space-3);
  background: linear-gradient(135deg, var(--cyber-green), var(--white), var(--cyber-green));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textGlow 4s ease-in-out infinite alternate;
  text-shadow: 0 0 30px rgba(0, 212, 170, 0.3);
}

@keyframes textGlow {
  from {
    background-position: 0% 50%;
    filter: drop-shadow(0 0 20px rgba(0, 212, 170, 0.4));
  }
  to {
    background-position: 100% 50%;
    filter: drop-shadow(0 0 30px rgba(0, 212, 170, 0.6));
  }
}

.header p {
  font-size: clamp(var(--text-lg), 3vw, var(--text-2xl));
  font-weight: var(--font-medium);
  margin: var(--space-3) 0;
  color: var(--light-gray);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.header .tagline {
  font-size: clamp(var(--text-base), 2.5vw, var(--text-xl));
  font-family: var(--font-mono);
  color: var(--security-orange);
  margin-top: var(--space-4);
  font-weight: var(--font-semibold);
  text-shadow: 0 0 10px rgba(255, 107, 53, 0.4);
  letter-spacing: 0.05em;
}

/* Professional About Section */
.about {
  margin: var(--space-16) auto; /* CENTER WITH AUTO MARGINS */
  background: rgba(11, 20, 38, 0.9);
  padding: var(--space-12);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg), var(--shadow-cyber);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 212, 170, 0.2);
  max-width: 900px;
  width: calc(100% - 2rem); /* ACCOUNT FOR MARGINS */
  position: relative;
  overflow: hidden;
  /* Force visibility to prevent disappearing during scroll */
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  transform: translateY(0) !important;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--cyber-green), var(--security-orange), var(--cyber-green));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.about h2 {
  color: var(--cyber-green);
  font-size: clamp(var(--text-2xl), 4vw, var(--text-4xl));
  font-weight: var(--font-bold);
  margin-bottom: var(--space-6);
  text-align: center;
  letter-spacing: -0.01em;
}

.about p {
  line-height: 1.7;
  font-size: clamp(var(--text-base), 2.5vw, var(--text-lg));
  font-weight: var(--font-normal);
  text-align: center;
  color: var(--light-gray);
  max-width: 700px;
  margin: 0 auto;
}

/* Professional Features Section */
.features {
  margin: var(--space-16) auto; /* CENTER WITH AUTO MARGINS */
  max-width: 1200px;
  width: calc(100% - 2rem); /* ACCOUNT FOR MARGINS */
  padding: 0 var(--space-4);
}

.features h2 {
  color: var(--cyber-green);
  font-size: clamp(var(--text-2xl), 4vw, var(--text-4xl));
  font-weight: var(--font-bold);
  text-align: center;
  margin-bottom: var(--space-12);
  letter-spacing: -0.01em;
}

/* Professional Feature Grid */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-8);
  padding: 0 var(--space-4);
}

.feature-card {
  background: rgba(11, 20, 38, 0.95);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  text-align: center;
  border: 1px solid rgba(0, 212, 170, 0.2);
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 170, 0.1), transparent);
  transition: left 0.6s ease;
}

.feature-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--cyber-green), var(--security-orange));
  transform: scaleX(0);
  transition: transform 0.4s ease;
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover::after {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-12px);
  box-shadow: var(--shadow-lg), 0 20px 40px rgba(0, 212, 170, 0.2);
  border-color: var(--cyber-green);
}

/* Professional Feature Icons */
.feature-icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-6);
  color: var(--cyber-green);
  filter: drop-shadow(0 0 15px rgba(0, 212, 170, 0.4));
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 0 25px rgba(0, 212, 170, 0.6));
}

.feature-card h3 {
  color: var(--white);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-4);
  letter-spacing: -0.01em;
}

.feature-card p {
  line-height: 1.7;
  color: var(--light-gray);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
}

.footer {
  margin-top: auto;
  padding: 2rem 1rem;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  width: 100%;
  border-top: 1px solid rgba(0, 255, 231, 0.2);
}

.footer p {
  margin: 0;
  font-size: clamp(0.8rem, 1.5vw, 1rem);
  opacity: 0.8;
}

/* Enhanced Cyber Loading Screen */
.cyber-loading-screen {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, var(--primary-blue), #000814, var(--secondary-blue));
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  overflow: hidden;
  transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
}

/* Matrix Rain Effect */
.matrix-rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  opacity: 0.3;
}

.matrix-column {
  position: absolute;
  top: -100%;
  font-family: var(--font-mono);
  font-size: 14px;
  color: var(--cyber-green);
  animation: matrixFall linear infinite;
  display: flex;
  flex-direction: column;
  text-shadow: 0 0 5px var(--cyber-green);
}

@keyframes matrixFall {
  to {
    transform: translateY(100vh);
  }
}

.matrix-column span {
  display: block;
  line-height: 1.2;
  animation: matrixFlicker 0.5s infinite;
}

@keyframes matrixFlicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* Cyber Grid Overlay */
.cyber-grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 170, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 170, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridPulse 4s ease-in-out infinite;
}

@keyframes gridPulse {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.5; }
}

/* Main Loading Content */
.loading-content {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 600px;
  width: 90%;
}

/* Cyber Logo */
.cyber-logo {
  margin-bottom: var(--space-12);
  position: relative;
}

.logo-shield {
  font-size: 4rem;
  color: var(--cyber-green);
  margin-bottom: var(--space-4);
  filter: drop-shadow(0 0 20px rgba(0, 212, 170, 0.6));
  animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
  from {
    filter: drop-shadow(0 0 20px rgba(0, 212, 170, 0.6));
    transform: scale(1);
  }
  to {
    filter: drop-shadow(0 0 30px rgba(0, 212, 170, 0.9));
    transform: scale(1.05);
  }
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.logo-main {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: var(--font-extrabold);
  color: var(--white);
  text-shadow: 0 0 20px rgba(0, 212, 170, 0.5);
  letter-spacing: 0.1em;
  font-family: var(--font-mono);
}

.logo-sub {
  font-size: clamp(0.8rem, 2vw, 1rem);
  color: var(--security-orange);
  font-weight: var(--font-medium);
  letter-spacing: 0.2em;
  text-transform: uppercase;
  font-family: var(--font-mono);
}

/* Progress Section */
.progress-section {
  margin-bottom: var(--space-12);
}

.progress-bar-container {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(0, 212, 170, 0.2);
  border-radius: 2px;
  margin-bottom: var(--space-4);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--cyber-green), var(--security-orange));
  border-radius: 2px;
  width: 0%;
  position: relative;
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.6);
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8));
  animation: progressShine 1.5s ease-in-out infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-20px); }
  100% { transform: translateX(20px); }
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--font-mono);
}

.loading-status {
  color: var(--cyber-green);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-shadow: 0 0 5px rgba(0, 212, 170, 0.5);
}

.progress-percentage {
  color: var(--security-orange);
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
}

/* Terminal Output */
.terminal-output {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--cyber-green);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
  margin-bottom: var(--space-8);
}

.terminal-header {
  background: var(--cyber-green);
  color: var(--primary-blue);
  padding: var(--space-2) var(--space-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--font-mono);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
}

.terminal-controls {
  display: flex;
  gap: var(--space-1);
}

.terminal-controls span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-blue);
  opacity: 0.7;
}

.terminal-body {
  padding: var(--space-4);
  font-family: var(--font-mono);
  font-size: var(--text-sm);
  color: var(--cyber-green);
  min-height: 60px;
  display: flex;
  align-items: center;
}

.cursor {
  animation: cursorBlink 1s infinite;
  margin-left: 2px;
}

@keyframes cursorBlink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Scanning Lines Effect */
.scan-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 212, 170, 0.03) 2px,
    rgba(0, 212, 170, 0.03) 4px
  );
  animation: scanMove 2s linear infinite;
  pointer-events: none;
}

@keyframes scanMove {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

/* Corner Brackets */
.corner-brackets {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.bracket {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 2px solid var(--cyber-green);
  opacity: 0.6;
}

.bracket.top-left {
  top: 20px;
  left: 20px;
  border-right: none;
  border-bottom: none;
  animation: bracketGlow 3s ease-in-out infinite;
}

.bracket.top-right {
  top: 20px;
  right: 20px;
  border-left: none;
  border-bottom: none;
  animation: bracketGlow 3s ease-in-out infinite 0.5s;
}

.bracket.bottom-left {
  bottom: 20px;
  left: 20px;
  border-right: none;
  border-top: none;
  animation: bracketGlow 3s ease-in-out infinite 1s;
}

.bracket.bottom-right {
  bottom: 20px;
  right: 20px;
  border-left: none;
  border-top: none;
  animation: bracketGlow 3s ease-in-out infinite 1.5s;
}

@keyframes bracketGlow {
  0%, 100% {
    opacity: 0.6;
    box-shadow: 0 0 5px rgba(0, 212, 170, 0.3);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 15px rgba(0, 212, 170, 0.8);
  }
}

/* Responsive Design for Loading Screen */
@media (max-width: 768px) {
  .cyber-logo {
    margin-bottom: var(--space-8);
  }

  .logo-shield {
    font-size: 3rem;
  }

  .terminal-output {
    margin-bottom: var(--space-6);
  }

  .bracket {
    width: 30px;
    height: 30px;
  }

  .bracket.top-left,
  .bracket.top-right {
    top: 15px;
  }

  .bracket.bottom-left,
  .bracket.bottom-right {
    bottom: 15px;
  }

  .bracket.top-left,
  .bracket.bottom-left {
    left: 15px;
  }

  .bracket.top-right,
  .bracket.bottom-right {
    right: 15px;
  }
}

.cube-container {
  perspective: clamp(400px, 80vw, 800px);
  margin: 3rem auto;
  height: clamp(100px, 20vw, 150px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.cube {
  width: clamp(80px, 15vw, 120px);
  height: clamp(80px, 15vw, 120px);
  position: relative;
  transform-style: preserve-3d;
  transform: rotateX(-30deg) rotateY(30deg);
  margin: auto;
  filter: drop-shadow(0 0 20px rgba(0, 255, 231, 0.5));
}

.face {
  position: absolute;
  width: clamp(80px, 15vw, 120px);
  height: clamp(80px, 15vw, 120px);
  background: linear-gradient(135deg, rgba(44, 83, 100, 0.9), rgba(15, 32, 39, 0.9));
  border: 2px solid #00ffe7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  color: #fff;
  box-shadow: 0 0 20px rgba(0, 255, 231, 0.4), inset 0 0 20px rgba(0, 255, 231, 0.1);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.face:hover {
  background: linear-gradient(135deg, rgba(0, 255, 231, 0.3), rgba(255, 0, 200, 0.3));
  box-shadow: 0 0 30px rgba(0, 255, 231, 0.8), inset 0 0 30px rgba(0, 255, 231, 0.2);
}

.front  { transform: rotateY(0deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.back   { transform: rotateY(180deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.right  { transform: rotateY(90deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.left   { transform: rotateY(-90deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.top    { transform: rotateX(90deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.bottom { transform: rotateX(-90deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }

/* Floating particles effect */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #00ffe7;
  border-radius: 50%;
  animation: float 6s linear infinite;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-10vh) rotate(360deg);
    opacity: 0;
  }
}

/* Professional Navigation Styles */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(11, 20, 38, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 212, 170, 0.2);
  padding: var(--space-4) 0;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--space-8);
}

.nav-logo span {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--cyber-green);
  text-shadow: 0 0 15px rgba(0, 212, 170, 0.4);
  letter-spacing: -0.01em;
}

.desktop-menu {
  display: flex;
  gap: var(--space-8);
}

.desktop-menu a {
  color: var(--light-gray);
  text-decoration: none;
  font-weight: var(--font-medium);
  font-size: var(--text-base);
  transition: all 0.3s ease;
  position: relative;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
}

.desktop-menu a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--cyber-green);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.desktop-menu a:hover {
  color: var(--cyber-green);
  background: rgba(0, 212, 170, 0.1);
}

.desktop-menu a:hover::after {
  width: 80%;
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu-btn span {
  width: 25px;
  height: 3px;
  background: #00ffe7;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobile-menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1001;
  display: none;
  opacity: 0;
}

.mobile-menu {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: linear-gradient(135deg, #0f2027, #2c5364);
  transform: translateX(100%);
  display: flex;
  flex-direction: column;
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(0, 255, 231, 0.2);
}

.mobile-menu-header span {
  font-size: 1.3rem;
  color: #00ffe7;
  font-weight: bold;
}

.mobile-menu-header button {
  background: none;
  border: none;
  color: #00ffe7;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-menu-items {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.mobile-menu-items a {
  color: #e0e0e0;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.mobile-menu-items a:hover {
  color: #00ffe7;
  background: rgba(0, 255, 231, 0.1);
  border-color: rgba(0, 255, 231, 0.3);
}

/* Contact Section Styles */
.contact-section {
  margin: 4rem 1rem;
  max-width: 1200px;
  width: 100%;
}

.contact-container {
  text-align: center;
}

.contact-section h2 {
  color: #00ffe7;
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  margin-bottom: 1rem;
  text-shadow: 0 0 10px #00ffe7aa;
}

.contact-section > .contact-container > p {
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  margin-bottom: 3rem;
  opacity: 0.9;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.contact-card {
  background: rgba(20, 20, 40, 0.8);
  padding: 2rem;
  border-radius: 15px;
  border: 1px solid rgba(0, 255, 231, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 231, 0.1), transparent);
  transition: left 0.5s ease;
}

.contact-card:hover::before {
  left: 100%;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 255, 231, 0.3);
  border-color: #00ffe7;
}

.contact-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 10px #00ffe7aa);
}

.contact-card h3 {
  color: #00ffe7;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.contact-content {
  color: var(--cyber-green);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-2);
}

.contact-description {
  color: var(--light-gray);
  font-size: var(--text-sm);
  margin-bottom: var(--space-4);
  opacity: 0.9;
}

.contact-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: linear-gradient(135deg, var(--cyber-green), var(--security-orange));
  color: var(--primary-blue);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-link:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 10px 25px rgba(0, 212, 170, 0.3);
}

.cta-section {
  background: rgba(11, 20, 38, 0.95);
  padding: var(--space-16);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(0, 212, 170, 0.3);
  backdrop-filter: blur(20px);
  position: relative;
  text-align: center;
}

.cta-content h3 {
  color: var(--cyber-green);
  font-size: clamp(var(--text-2xl), 4vw, var(--text-4xl));
  font-weight: var(--font-bold);
  margin-bottom: var(--space-4);
  letter-spacing: -0.01em;
}

.cta-content p {
  color: var(--light-gray);
  font-size: clamp(var(--text-base), 2.5vw, var(--text-lg));
  line-height: 1.6;
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: var(--space-12);
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-lg);
  font-weight: var(--font-semibold);
  font-size: var(--text-lg);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.cta-button.primary {
  background: linear-gradient(135deg, var(--cyber-green), var(--security-orange));
  color: var(--primary-blue);
  box-shadow: var(--shadow-lg);
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg), 0 15px 35px rgba(0, 212, 170, 0.4);
}

.cta-button.secondary {
  background: transparent;
  color: var(--cyber-green);
  border: 2px solid var(--cyber-green);
}

.cta-button.secondary:hover {
  background: var(--cyber-green);
  color: var(--primary-blue);
  transform: translateY(-3px);
}

.button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.cta-button:hover .button-glow {
  left: 100%;
}

.quick-stats {
  display: flex;
  justify-content: center;
  gap: var(--space-8);
  flex-wrap: wrap;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: var(--font-extrabold);
  color: var(--cyber-green);
  font-family: var(--font-mono);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--light-gray);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Professional Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: var(--space-16) var(--space-4);
  margin-top: 80px;
  text-align: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: -1;
}

.cyber-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 170, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 170, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.floating-icons {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-icons i {
  position: absolute;
  color: var(--cyber-green);
  opacity: 0.1;
  font-size: var(--text-2xl);
  animation: float 6s ease-in-out infinite;
}

.floating-icons i:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.floating-icons i:nth-child(2) { top: 60%; left: 80%; animation-delay: 1s; }
.floating-icons i:nth-child(3) { top: 30%; left: 70%; animation-delay: 2s; }
.floating-icons i:nth-child(4) { top: 80%; left: 20%; animation-delay: 3s; }
.floating-icons i:nth-child(5) { top: 10%; left: 60%; animation-delay: 4s; }
.floating-icons i:nth-child(6) { top: 70%; left: 40%; animation-delay: 5s; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
  max-width: 800px;
  z-index: 1;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  padding: var(--space-2) var(--space-4);
  border-radius: 50px;
  margin-bottom: var(--space-6);
  color: var(--cyber-green);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.hero-content h1 {
  font-size: clamp(var(--text-4xl), 8vw, var(--text-6xl));
  font-weight: var(--font-extrabold);
  color: var(--white);
  margin-bottom: var(--space-6);
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.hero-content p {
  font-size: clamp(var(--text-lg), 3vw, var(--text-xl));
  color: var(--light-gray);
  margin-bottom: var(--space-8);
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: var(--space-16);
}

.cta-primary, .cta-secondary {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-semibold);
  font-size: var(--text-base);
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.cta-primary {
  background: linear-gradient(135deg, var(--cyber-green), var(--security-orange));
  color: var(--primary-blue);
  box-shadow: var(--shadow-lg);
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 10px 30px rgba(0, 212, 170, 0.3);
}

.cta-secondary {
  background: transparent;
  color: var(--cyber-green);
  border: 2px solid var(--cyber-green);
}

.cta-secondary:hover {
  background: var(--cyber-green);
  color: var(--primary-blue);
  transform: translateY(-2px);
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-8);
  max-width: 800px;
  width: 100%;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: var(--text-4xl);
  font-weight: var(--font-extrabold);
  color: var(--cyber-green);
  margin-bottom: var(--space-2);
  font-family: var(--font-mono);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--light-gray);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Optimized Three.js Scene Styles */
.three-scene-container, .three-fallback {
  width: 100%;
  height: 400px;
  margin: var(--space-8) 0;
  border-radius: var(--radius-xl);
  overflow: hidden;
  border: 1px solid rgba(0, 212, 170, 0.3);
  box-shadow: var(--shadow-lg), var(--shadow-cyber);
  background: rgba(11, 20, 38, 0.8);
  backdrop-filter: blur(20px);
}

.three-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--cyber-green);
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
}

.three-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--cyber-green);
  font-size: var(--text-base);
  gap: var(--space-4);
}

/* Testimonials Section */
.testimonials-section {
  padding: var(--space-16) var(--space-4);
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.testimonials-container {
  text-align: center;
}

.section-header {
  margin-bottom: var(--space-12);
}

.section-header h2 {
  color: var(--cyber-green);
  font-size: clamp(var(--text-2xl), 4vw, var(--text-4xl));
  font-weight: var(--font-bold);
  margin-bottom: var(--space-4);
  letter-spacing: -0.01em;
}

.section-header p {
  color: var(--light-gray);
  font-size: clamp(var(--text-base), 2.5vw, var(--text-lg));
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-16);
}

.testimonial-card {
  background: rgba(11, 20, 38, 0.95);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(0, 212, 170, 0.2);
  backdrop-filter: blur(20px);
  transition: all 0.4s ease;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--cyber-green), var(--security-orange));
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.testimonial-card:hover::before {
  transform: scaleX(1);
}

.testimonial-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg), 0 20px 40px rgba(0, 212, 170, 0.15);
  border-color: var(--cyber-green);
}

.testimonial-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.student-avatar {
  font-size: var(--text-4xl);
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 212, 170, 0.1);
  border-radius: 50%;
  border: 2px solid rgba(0, 212, 170, 0.3);
}

.student-info h4 {
  color: var(--white);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
}

.student-role {
  color: var(--cyber-green);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  margin-bottom: var(--space-1);
}

.student-company {
  color: var(--light-gray);
  font-size: var(--text-sm);
  margin: 0;
}

.testimonial-quote {
  color: var(--light-gray);
  font-size: var(--text-base);
  line-height: 1.7;
  font-style: italic;
  margin: 0 0 var(--space-6) 0;
  position: relative;
}

.testimonial-quote::before {
  content: '"';
  font-size: var(--text-4xl);
  color: var(--cyber-green);
  position: absolute;
  top: -10px;
  left: -10px;
  font-family: serif;
}

.achievement-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  padding: var(--space-2) var(--space-4);
  border-radius: 50px;
  color: var(--security-orange);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.join-cta {
  background: rgba(11, 20, 38, 0.9);
  padding: var(--space-12);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(0, 212, 170, 0.3);
  backdrop-filter: blur(20px);
}

.join-cta h3 {
  color: var(--cyber-green);
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-4);
}

.join-cta p {
  color: var(--light-gray);
  font-size: var(--text-lg);
  margin-bottom: var(--space-8);
}

.join-button, .leadership-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  background: linear-gradient(135deg, var(--cyber-green), var(--security-orange));
  color: var(--primary-blue);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-lg);
  font-weight: var(--font-semibold);
  font-size: var(--text-lg);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.join-button:hover, .leadership-button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg), 0 15px 35px rgba(0, 212, 170, 0.3);
}

/* Team Section */
.team-section {
  padding: var(--space-16) var(--space-4);
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.team-container {
  text-align: center;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-16);
}

.team-member-card {
  background: rgba(11, 20, 38, 0.95);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(0, 212, 170, 0.2);
  backdrop-filter: blur(20px);
  transition: all 0.4s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.team-member-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg), 0 25px 50px rgba(0, 212, 170, 0.2);
  border-color: var(--cyber-green);
}

.member-image {
  position: relative;
  margin-bottom: var(--space-6);
  display: inline-block;
}

.avatar {
  font-size: var(--text-5xl);
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 212, 170, 0.1);
  border-radius: 50%;
  border: 3px solid rgba(0, 212, 170, 0.3);
  margin: 0 auto;
  transition: all 0.3s ease;
}

.member-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 212, 170, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.team-member-card:hover .member-overlay {
  opacity: 1;
}

.team-member-card:hover .avatar {
  transform: scale(1.05);
}

.social-links {
  display: flex;
  gap: var(--space-4);
}

.social-links a {
  color: var(--primary-blue);
  font-size: var(--text-xl);
  transition: all 0.3s ease;
}

.social-links a:hover {
  transform: scale(1.2);
}

.member-info h4 {
  color: var(--white);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-2);
}

.member-role {
  color: var(--cyber-green);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
}

.member-year {
  color: var(--light-gray);
  font-size: var(--text-sm);
  margin-bottom: var(--space-2);
}

.member-expertise {
  color: var(--security-orange);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  margin-bottom: var(--space-4);
}

.member-bio {
  color: var(--light-gray);
  font-size: var(--text-sm);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.certifications {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  justify-content: center;
}

.cert-badge {
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  color: var(--security-orange);
  padding: var(--space-1) var(--space-3);
  border-radius: 50px;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.join-team-cta {
  background: rgba(11, 20, 38, 0.9);
  padding: var(--space-12);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(0, 212, 170, 0.3);
  backdrop-filter: blur(20px);
}

.join-team-cta h3 {
  color: var(--cyber-green);
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-4);
}

.join-team-cta p {
  color: var(--light-gray);
  font-size: var(--text-lg);
  margin-bottom: var(--space-8);
}

/* Adjust header margin for fixed nav */
.header {
  margin-top: 6rem;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  .desktop-menu {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .nav-container {
    padding: 0 var(--space-4);
  }

  .header {
    margin-top: 5rem;
  }

  .hero-section {
    padding: var(--space-12) var(--space-4);
    margin-top: 70px;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }

  .about, .features {
    margin: var(--space-12) var(--space-2);
    padding: var(--space-8);
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
    padding: 0 var(--space-2);
  }

  .feature-card {
    padding: var(--space-6);
  }

  .cube-container {
    margin: var(--space-8) auto;
  }

  .mobile-menu {
    width: 280px;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .team-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .quick-stats {
    gap: var(--space-4);
  }
}

@media (max-width: 480px) {
  .header h1 {
    letter-spacing: -0.01em;
  }

  .header {
    margin-top: 4.5rem;
  }

  .hero-section {
    padding: var(--space-8) var(--space-2);
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .about, .features {
    margin: var(--space-8) var(--space-1);
    padding: var(--space-6);
  }

  .feature-card {
    padding: var(--space-4);
  }

  .footer {
    padding: var(--space-6) var(--space-2);
  }

  .mobile-menu {
    width: 100%;
  }

  .nav-logo span {
    font-size: var(--text-xl);
  }

  .testimonials-section, .team-section {
    padding: var(--space-12) var(--space-2);
  }

  .testimonial-card, .team-member-card {
    padding: var(--space-6);
  }

  .contact-section {
    padding: var(--space-12) var(--space-2);
  }

  .contact-card {
    padding: var(--space-6);
  }

  .cta-section {
    padding: var(--space-8) var(--space-4);
  }

  .cta-button {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
  }

  .quick-stats {
    flex-direction: column;
    gap: var(--space-3);
  }
}
