import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
// ScrollTrigger removed to prevent scroll interference issues

function ContactSection() {
  const sectionRef = useRef();
  const cardsRef = useRef();

  useEffect(() => {
    // Disable scroll animations to prevent content disappearing - keep cards visible
    const cards = cardsRef.current.children;
    // Simply ensure all cards are visible immediately
    gsap.set(cards, { opacity: 1, y: 0, scale: 1 });

    // Original scroll animation commented out to fix scrolling issues
    /*
    gsap.fromTo(cards,
      { opacity: 0, y: 30, scale: 0.9 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.6,
        stagger: 0.1,
        ease: "back.out(1.7)",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );
    */
  }, []);

  const contactInfo = [
    {
      icon: "fas fa-envelope",
      title: "Email Us",
      content: "<EMAIL>",
      description: "Get answers to your questions",
      link: "mailto:<EMAIL>",
      cta: "Send Email"
    },
    {
      icon: "fab fa-linkedin",
      title: "LinkedIn",
      content: "Connect with our community",
      description: "Follow for updates and opportunities",
      link: "https://linkedin.com/company/djs-isaca",
      cta: "Follow Us"
    },
    {
      icon: "fab fa-discord",
      title: "Discord Server",
      content: "Join our active community",
      description: "Chat, collaborate, and learn together",
      link: "#",
      cta: "Join Discord"
    },
    {
      icon: "fas fa-calendar-alt",
      title: "Events",
      content: "Upcoming workshops & CTFs",
      description: "Never miss an opportunity to learn",
      link: "#",
      cta: "View Events"
    }
  ];

  return (
    <section id="contact" className="contact-section" ref={sectionRef}>
      <div className="contact-container">
        <h2>Connect & Get Involved</h2>
        <p>Ready to start your cybersecurity journey? Here's how you can connect with our community and stay updated on all opportunities.</p>

        <div className="contact-grid" ref={cardsRef}>
          {contactInfo.map((item, index) => (
            <div key={index} className="contact-card">
              <div className="contact-icon">
                <i className={item.icon}></i>
              </div>
              <h3>{item.title}</h3>
              <p className="contact-content">{item.content}</p>
              <p className="contact-description">{item.description}</p>
              <a
                href={item.link}
                target="_blank"
                rel="noopener noreferrer"
                className="contact-link"
              >
                <span>{item.cta}</span>
                <i className="fas fa-arrow-right"></i>
              </a>
            </div>
          ))}
        </div>

        <div className="cta-section">
          <div className="cta-content">
            <h3>Ready to Secure Your Future?</h3>
            <p>Join 200+ students who are building careers in cybersecurity. Get access to exclusive workshops, industry connections, and hands-on training that employers value.</p>

            <div className="cta-buttons">
              <button className="cta-button primary">
                <i className="fas fa-rocket"></i>
                <span>Join DJS ISACA</span>
                <div className="button-glow"></div>
              </button>

              <button className="cta-button secondary">
                <i className="fas fa-calendar"></i>
                <span>Attend Next Event</span>
              </button>
            </div>

            <div className="quick-stats">
              <div className="stat">
                <span className="stat-number">95%</span>
                <span className="stat-label">Placement Rate</span>
              </div>
              <div className="stat">
                <span className="stat-number">₹12L</span>
                <span className="stat-label">Avg. Package</span>
              </div>
              <div className="stat">
                <span className="stat-number">50+</span>
                <span className="stat-label">Industry Partners</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default ContactSection;
