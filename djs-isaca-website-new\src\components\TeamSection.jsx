import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

function TeamSection() {
  const sectionRef = useRef();
  const teamRef = useRef();

  useEffect(() => {
    // Disable scroll animations to prevent content disappearing - keep members visible
    const members = teamRef.current.children;
    // Simply ensure all members are visible immediately
    gsap.set(members, { opacity: 1, y: 0, rotateY: 0 });

    // Original scroll animation commented out to fix scrolling issues
    /*
    gsap.fromTo(members,
      { opacity: 0, y: 30, rotateY: 15 },
      {
        opacity: 1,
        y: 0,
        rotateY: 0,
        duration: 0.8,
        stagger: 0.15,
        ease: "power2.out",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 75%",
          end: "bottom 25%",
          toggleActions: "play none none reverse"
        }
      }
    );
    */
  }, []);

  const teamMembers = [
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Chapter President",
      year: "Final Year, Computer Engineering",
      expertise: "Penetration Testing & Red Team Operations",
      image: "🎯",
      bio: "Leading our chapter's mission to create industry-ready cybersecurity professionals. Passionate about ethical hacking and building inclusive tech communities.",
      certifications: ["CEH", "OSCP"],
      linkedin: "#",
      github: "#"
    },
    {
      name: "Kavya Nair",
      role: "Technical Lead",
      year: "Third Year, Information Technology",
      expertise: "Digital Forensics & Incident Response",
      image: "🔍",
      bio: "Organizing technical workshops and CTF events. Believes in learning by doing and making cybersecurity accessible to everyone.",
      certifications: ["GCIH", "GCFA"],
      linkedin: "#",
      github: "#"
    },
    {
      name: "Vikram Singh",
      role: "Events Coordinator",
      year: "Third Year, Computer Engineering",
      expertise: "Network Security & Cloud Security",
      image: "🌐",
      bio: "Connecting students with industry professionals and creating meaningful learning experiences through events and workshops.",
      certifications: ["CCNA Security", "AWS Security"],
      linkedin: "#",
      github: "#"
    },
    {
      name: "Ananya Joshi",
      role: "Community Manager",
      year: "Second Year, Electronics Engineering",
      expertise: "IoT Security & Hardware Hacking",
      image: "⚡",
      bio: "Building an inclusive community where students from all backgrounds can explore cybersecurity and find their passion.",
      certifications: ["CompTIA Security+"],
      linkedin: "#",
      github: "#"
    }
  ];

  return (
    <section id="team" className="team-section" ref={sectionRef}>
      <div className="team-container">
        <div className="section-header">
          <h2>Meet Our Leadership Team</h2>
          <p>Passionate students leading the charge in cybersecurity education and community building</p>
        </div>
        
        <div className="team-grid" ref={teamRef}>
          {teamMembers.map((member, index) => (
            <div key={index} className="team-member-card">
              <div className="member-image">
                <div className="avatar">{member.image}</div>
                <div className="member-overlay">
                  <div className="social-links">
                    <a href={member.linkedin} aria-label="LinkedIn">
                      <i className="fab fa-linkedin"></i>
                    </a>
                    <a href={member.github} aria-label="GitHub">
                      <i className="fab fa-github"></i>
                    </a>
                  </div>
                </div>
              </div>
              
              <div className="member-info">
                <h4>{member.name}</h4>
                <p className="member-role">{member.role}</p>
                <p className="member-year">{member.year}</p>
                <p className="member-expertise">{member.expertise}</p>
                
                <p className="member-bio">{member.bio}</p>
                
                <div className="certifications">
                  {member.certifications.map((cert, certIndex) => (
                    <span key={certIndex} className="cert-badge">{cert}</span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="join-team-cta">
          <h3>Want to Lead with Us?</h3>
          <p>We're always looking for passionate students to join our leadership team</p>
          <button className="leadership-button">
            <i className="fas fa-users"></i>
            <span>Apply for Leadership</span>
          </button>
        </div>
      </div>
    </section>
  );
}

export default TeamSection;
