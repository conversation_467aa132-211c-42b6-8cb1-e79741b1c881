<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scroll Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #0f2027;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(0, 212, 170, 0.1);
            border: 1px solid rgba(0, 212, 170, 0.3);
            border-radius: 10px;
        }
        .scroll-test {
            height: 200vh;
            background: linear-gradient(to bottom, #0f2027, #203a43, #2c5364);
            padding: 20px;
            margin: 20px 0;
        }
        .test-content {
            position: sticky;
            top: 20px;
            background: rgba(0, 212, 170, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Scroll Test Page</h1>
    <p>This page tests if content remains visible during scrolling.</p>
    
    <div class="test-section">
        <h2>Test Section 1</h2>
        <p>This content should remain visible when scrolling.</p>
    </div>
    
    <div class="scroll-test">
        <div class="test-content">
            <h2>Scrollable Content</h2>
            <p>Scroll down to test if content disappears...</p>
            <p>This content should stay visible throughout the scroll.</p>
        </div>
        
        <div style="height: 100vh; display: flex; align-items: center; justify-content: center;">
            <h3>Middle of scroll area</h3>
        </div>
        
        <div class="test-content">
            <h2>Bottom Content</h2>
            <p>If you can see this, scrolling is working correctly!</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Final Test Section</h2>
        <p>All content should be visible. No blank screens!</p>
    </div>
    
    <script>
        // Test script to monitor scroll behavior
        let scrollCount = 0;
        window.addEventListener('scroll', () => {
            scrollCount++;
            console.log(`Scroll event ${scrollCount}: Y position = ${window.scrollY}`);
            
            // Check if any content is hidden
            const sections = document.querySelectorAll('.test-section, .test-content');
            sections.forEach((section, index) => {
                const rect = section.getBoundingClientRect();
                const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
                if (!isVisible && rect.top < window.innerHeight) {
                    console.warn(`Section ${index} may be hidden during scroll`);
                }
            });
        });
        
        console.log('Scroll test page loaded. Check console for scroll events.');
    </script>
</body>
</html>
