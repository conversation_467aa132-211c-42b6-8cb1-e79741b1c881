// 🚨 EMERGENCY NUCLEAR VISIBILITY FIX 🚨
// Run this in browser console if content still disappears

console.log('🚨 EMERGENCY NUCLEAR FIX ACTIVATED 🚨');

function nuclearVisibilityFix() {
  console.log('💥 Applying nuclear visibility fixes...');
  
  // NUCLEAR OPTION 1: Force ALL elements to be visible
  const allElements = document.querySelectorAll('*');
  allElements.forEach((el, index) => {
    if (el.tagName !== 'SCRIPT' && el.tagName !== 'STYLE') {
      el.style.opacity = '1';
      el.style.visibility = 'visible';
      el.style.display = el.style.display === 'none' ? 'block' : el.style.display || 'block';
      el.style.transform = 'none';
      el.style.transition = 'none';
      el.style.animation = 'none';
      el.style.contain = 'none';
      el.style.willChange = 'auto';
      el.style.backfaceVisibility = 'visible';
      el.style.overflow = 'visible';
      el.style.clip = 'none';
      el.style.clipPath = 'none';
      el.style.position = el.style.position || 'relative';
      el.style.zIndex = el.style.zIndex || '1';
    }
  });
  
  // NUCLEAR OPTION 2: Force specific sections
  const criticalSections = [
    '#about', '.about',
    '#features', '.features', 
    '.team-section', '.testimonials-section', '.contact-section',
    'section', 'main', 'article', 'div'
  ];
  
  criticalSections.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(el => {
      el.style.opacity = '1';
      el.style.visibility = 'visible';
      el.style.display = 'block';
      el.style.transform = 'none';
      el.style.position = 'relative';
      el.style.zIndex = '999';
      el.style.background = el.style.background || 'rgba(11, 20, 38, 0.9)';
    });
  });
  
  // NUCLEAR OPTION 3: Remove any problematic CSS
  const problematicStyles = document.querySelectorAll('style');
  problematicStyles.forEach(style => {
    if (style.textContent.includes('opacity: 0') || 
        style.textContent.includes('visibility: hidden') ||
        style.textContent.includes('display: none')) {
      console.warn('🚨 Found problematic style, neutralizing...', style);
      // Don't remove, just log for now
    }
  });
  
  // NUCLEAR OPTION 4: Force body and html
  document.body.style.opacity = '1';
  document.body.style.visibility = 'visible';
  document.body.style.display = 'block';
  document.body.style.background = '#0B1426';
  document.documentElement.style.opacity = '1';
  document.documentElement.style.visibility = 'visible';
  
  console.log('💥 Nuclear visibility fix applied!');
}

// Apply immediately
nuclearVisibilityFix();

// Apply on scroll
let scrollFixCount = 0;
window.addEventListener('scroll', () => {
  scrollFixCount++;
  if (scrollFixCount % 5 === 0) { // Every 5 scroll events
    nuclearVisibilityFix();
  }
});

// Apply every 500ms as emergency backup
setInterval(nuclearVisibilityFix, 500);

// Export for manual use
window.emergencyFix = nuclearVisibilityFix;

console.log('🚨 EMERGENCY FIX LOADED! 🚨');
console.log('💡 If content disappears, run: emergencyFix()');

// Auto-detect blank screen and fix
function detectBlankScreen() {
  const aboutSection = document.querySelector('#about, .about');
  if (aboutSection) {
    const rect = aboutSection.getBoundingClientRect();
    const styles = window.getComputedStyle(aboutSection);
    
    if (styles.opacity === '0' || styles.visibility === 'hidden' || styles.display === 'none') {
      console.error('🚨 BLANK SCREEN DETECTED! Applying emergency fix...');
      nuclearVisibilityFix();
      alert('🚨 Blank screen detected and fixed! Your limbs are safe!');
    }
  }
}

// Check for blank screen every 1 second
setInterval(detectBlankScreen, 1000);

console.log('🛡️ Emergency monitoring active. Your limbs are protected!');
